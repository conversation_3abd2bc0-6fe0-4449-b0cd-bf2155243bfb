import prisma from '@/app/lib/prisma';
import { auth } from "@/auth";
import ComicTile from "@/app/components/comicTile";
import RequireSignIn from "@/app/auth/email-signin/page";
import { BookOpenIcon } from '@heroicons/react/24/outline';
import AdsBanner from "@/app/components/AdsBanner";
import { BookShelfData } from '@/app/types';

async function getBooks(userId: string): Promise<BookShelfData> {
  const bookShelves = await prisma.bookShelf.findMany({
    where: { userId: userId },
    include: { book: { include: { activeResource: { include: { chapters: { orderBy: { ind: "asc" } } } } } }, lastRead: true },
    orderBy: [{ createdAt: "desc" }],
  });

  const readingBooks = bookShelves.filter(bs => bs.lastRead.id != bs.book.activeResource!.chapters.at(-1)?.id);
  const completedBooks = bookShelves.filter(bs => bs.book.continued && bs.lastRead.id == bs.book.activeResource!.chapters.at(-1)?.id);
  const discontinuedCompletedBooks = bookShelves.filter(bs => !bs.book.continued && bs.lastRead.id == bs.book.activeResource!.chapters.at(-1)?.id);

  return { readingBooks, completedBooks, discontinuedCompletedBooks };
}

interface PageProps {
  searchParams: Promise<{ tab?: string }>;
}

export default async function BookShelfPage({ searchParams }: PageProps) {
  const session = await auth();
  if (!session) return (
    <RequireSignIn />
  )
  // console.log("session:", session);
  const userId = session?.user?.id;
  console.log("user id:", userId);

  if (!userId) {
    return (
      <RequireSignIn />
    )
  }

  const { readingBooks, completedBooks, discontinuedCompletedBooks } = await getBooks(userId);
  const resolvedSearchParams = await searchParams;
  let books;
  switch (resolvedSearchParams.tab) {
    case "reading":
      books = readingBooks;
      break;
    case "waiting":
      books = completedBooks;
      break;
    case "finished":
      books = discontinuedCompletedBooks;
      break;
  }


  const tabs = [
    { name: '閱讀中', href: '/my-book-shelf?tab=reading', count: readingBooks.length, current: resolvedSearchParams.tab == "reading" },
    { name: '追更', href: '/my-book-shelf?tab=waiting', count: completedBooks.length, current: resolvedSearchParams.tab == "waiting" },
    { name: '閱讀完', href: '/my-book-shelf?tab=finished', count: discontinuedCompletedBooks.length, current: resolvedSearchParams.tab == "finished" },
  ]

  function classNames(...classes: any[]) {
    return classes.filter(Boolean).join(' ')
  }

  return (
    <div className='px-1'>
      <div className='flex items-center space-x-1 text-xl my-2 sm:my-4 text-gray-600 dark:text-gray-300'>
        <BookOpenIcon className="h-7 w-7 shrink-0" />
        <div>我的書架</div>
      </div>

      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav aria-label="Tabs" className="-mb-px flex space-x-4">
          {tabs.map((tab) => (
            <a
              key={tab.name}
              href={tab.href}
              aria-current={tab.current ? 'page' : undefined}
              className={classNames(
                tab.current
                  ? 'border-pink-500 text-pink-600 dark:text-pink-400'
                  : 'border-transparent text-gray-500 hover:border-gray-200 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600',
                'flex items-center whitespace-nowrap border-b-2 px-2 py-4 font-medium',
              )}
            >
              {tab.name}
              {tab.count ? (
                <span
                  className={classNames(
                    tab.current ? 'bg-pink-100 text-pink-600 dark:bg-pink-900 dark:text-pink-300' : 'bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-300',
                    'ml-3 rounded-full px-2 py-0.5 text-xs font-medium md:inline-block',
                  )}
                >
                  {tab.count}
                </span>
              ) : null}
            </a>
          ))}
        </nav>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 my-2">
        <div className='flex justify-center'>
          <AdsBanner id="ckunfc3nm01420hj9kqjfo2ea" />
        </div>
        <div className='flex justify-center'>
          <AdsBanner id="ckv6gba8c24350im3gr9wwg45" />
        </div>
      </div>

      <div className='grid grid-cols-1 sm:grid-cols-4 md:grid-cols-6 gap-2 sm:gap-4'>
        {books && books.map((bs: any) => {
          const chapters = bs.book.activeResource?.chapters;
          const lastRead = bs.lastRead;
          const lastChapter = chapters.at(-1);
          return (
            <ComicTile key={bs.bookId} book={bs.book} lastRead={lastRead} lastChapter={lastChapter} />)
        })}
      </div>

    </div>
  )
}