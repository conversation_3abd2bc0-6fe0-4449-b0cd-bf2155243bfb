// import { searchBooks } from '@/app/lib/elasticsearch'
import { searchBooks } from '@/app/lib/meilisearch'
import ComicTile from "@/app/components/comicTile";
import SearchBox from './searchBox';
import Pagination from '@/app/components/pagination';
import {
  MagnifyingGlassIcon,
} from '@heroicons/react/24/outline'
import Script from 'next/script';
import AdsBanner from '@/app/components/AdsBanner';

const itemPerPage = 18;

async function search(term: string, currentPage: number) {
  if (!term) {
    return { estimatedTotalHits: 0, hits: [] };
  }
  const body = await searchBooks(term, currentPage, itemPerPage);
  // console.log("body:", body);

  // let books = body.hits.hits.map((hit: any) => {
  //   return { id: hit._id, ...hit._source };
  // });

  return body;
}

type Props = {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}

export default async function SearchPage({ searchParams }: Props) {
  const resolvedSearchParams = await searchParams;
  const term = resolvedSearchParams.term as string;
  const currentPage = resolvedSearchParams.page ? Number(resolvedSearchParams.page) : 0;

  const body = await search(term, currentPage);
  const totalNum = body.estimatedTotalHits;
  const total = Math.ceil(Number(totalNum) / itemPerPage);
  let books = body.hits;
  return (
    <div className='container mx-auto px-4'>
      <div className='flex items-center space-x-2 text-xl my-4 sm:my-6 text-foreground'>
        <MagnifyingGlassIcon className="h-7 w-7 shrink-0" />
        <div className="font-medium">搜索</div>
      </div>

      <div className='flex justify-center my-6'>
        <AdsBanner id="ckunfc3nm01420hj9kqjfo2ea" />
      </div>
      <SearchBox term={term} />

      <div className='mt-4 sm:mt-6'>
        <div className='text-xl flex justify-center text-foreground font-medium mb-6'>{total} 個搜索結果</div>
        <div className='grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 gap-3 sm:gap-4 my-6'>
          {books.map((book: any) =>
            <ComicTile book={book} key={book.id} lastRead={null} lastChapter={null} />)}
        </div>
      </div>

      <div className='flex justify-center my-6'>
        <AdsBanner id="ckv6gba8c24350im3gr9wwg45" />
      </div>

      <div className='grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-8 my-6 sm:my-8'>
        <div className='flex justify-center'>
          <ins id="__clb-1854687" className="w-full" />
        </div>
        <div className='flex justify-center relative'>
          <ins id="901415" data-width="728" data-height="102" className="w-full"></ins>
        </div>
      </div>

      <div className='my-8'>
        <Pagination path={`search?term=${term}`} current={currentPage} total={total} />
      </div>

      {/* Clickadu */}
      <Script
        data-cfasync="false"
        type="text/javascript"
        src="//chaseherbalpasty.com/lv/esnk/1854687/code.js"
        async
        id="__clb-1854687"
      />
    </div>
  )
}